import React from 'react';
import { User, Bell, Shield, Database } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import Header from '../components/layout/Header';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

const Settings: React.FC = () => {
  const { user } = useAuth();

  const settingsSections = [
    {
      title: 'Profile Settings',
      description: 'Manage your personal information and preferences',
      icon: User,
      items: [
        { label: 'Full Name', value: user?.name },
        { label: 'Role', value: user?.role },
        { label: 'Username', value: user?.username }
      ]
    },
    {
      title: 'Notifications',
      description: 'Configure your notification preferences',
      icon: Bell,
      items: [
        { label: 'Email Notifications', value: 'Enabled' },
        { label: 'Push Notifications', value: 'Enabled' },
        { label: 'SMS Notifications', value: 'Disabled' }
      ]
    },
    {
      title: 'Security',
      description: 'Manage your account security settings',
      icon: Shield,
      items: [
        { label: 'Two-Factor Authentication', value: 'Disabled' },
        { label: 'Last Password Change', value: '30 days ago' },
        { label: 'Active Sessions', value: '2 sessions' }
      ]
    },
    {
      title: 'Data & Privacy',
      description: 'Control your data and privacy settings',
      icon: Database,
      items: [
        { label: 'Data Export', value: 'Available' },
        { label: 'Privacy Level', value: 'Standard' },
        { label: 'Data Retention', value: '2 years' }
      ]
    }
  ];

  return (
    <div>
      <Header title="Settings" />
      
      <div className="p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          {settingsSections.map((section, index) => {
            const Icon = section.icon;
            return (
              <Card key={index} className="p-6">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                    <p className="text-sm text-gray-600">{section.description}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  {section.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex justify-between items-center py-2">
                      <span className="text-sm font-medium text-gray-700">{item.label}</span>
                      <span className="text-sm text-gray-600">{item.value}</span>
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-end">
                  <Button variant="secondary" size="sm">
                    Configure
                  </Button>
                </div>
              </Card>
            );
          })}
          
          <Card className="p-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
              <p className="text-sm text-gray-600 mb-4">
                Contact our support team for assistance with your account or any questions.
              </p>
              <Button>Contact Support</Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Settings;