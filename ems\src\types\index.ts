export interface Employee {
  id: string;
  name: string;
  email: string;
  phone: string;
  department: string;
  role: string;
  joiningDate: string;
  status: 'Active' | 'Inactive';
  avatar?: string;
}

export interface User {
  id: string;
  username: string;
  role: 'Admin' | 'HR' | 'Employee';
  name: string;
}

export interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => boolean;
  logout: () => void;
  isAuthenticated: boolean;
}

export interface EmployeeContextType {
  employees: Employee[];
  addEmployee: (employee: Omit<Employee, 'id'>) => void;
  updateEmployee: (id: string, employee: Omit<Employee, 'id'>) => void;
  deleteEmployee: (id: string) => void;
  getEmployee: (id: string) => Employee | undefined;
}

export interface EmployeeFormData {
  name: string;
  email: string;
  phone: string;
  department: string;
  role: string;
  joiningDate: string;
  status: 'Active' | 'Inactive';
}