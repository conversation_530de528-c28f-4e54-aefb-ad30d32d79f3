import React from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Edit, Mail, Phone, Calendar, Building, User, Clock } from 'lucide-react';
import { useEmployees } from '../context/EmployeeContext';
import { useAuth } from '../context/AuthContext';
import Header from '../components/layout/Header';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';

const EmployeeDetail: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { getEmployee } = useEmployees();
  const { user } = useAuth();
  
  const employee = id ? getEmployee(id) : null;
  const canModify = user?.role === 'Admin' || user?.role === 'HR';

  if (!employee) {
    return (
      <div>
        <Header title="Employee Not Found" />
        <div className="p-6">
          <div className="text-center py-12">
            <p className="text-gray-600 mb-4">The employee you're looking for doesn't exist.</p>
            <Button onClick={() => navigate('/employees')}>
              Back to Employees
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const details = [
    { label: 'Email', value: employee.email, icon: Mail },
    { label: 'Phone', value: employee.phone, icon: Phone },
    { label: 'Department', value: employee.department, icon: Building },
    { label: 'Role', value: employee.role, icon: User },
    { label: 'Joining Date', value: new Date(employee.joiningDate).toLocaleDateString(), icon: Calendar },
    { label: 'Employment Status', value: employee.status, icon: Clock }
  ];

  return (
    <div>
      <Header title="Employee Details" />
      
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6 flex items-center justify-between">
            <Button
              variant="secondary"
              onClick={() => navigate('/employees')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Employees</span>
            </Button>
            
            {canModify && (
              <Link to={`/employees/edit/${employee.id}`}>
                <Button className="flex items-center space-x-2">
                  <Edit className="w-4 h-4" />
                  <span>Edit Employee</span>
                </Button>
              </Link>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profile Card */}
            <Card className="p-6 lg:col-span-1">
              <div className="text-center">
                <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-white">
                    {employee.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">{employee.name}</h2>
                <p className="text-gray-600 mb-4">{employee.role}</p>
                <Badge variant={employee.status === 'Active' ? 'success' : 'danger'} className="text-sm">
                  {employee.status}
                </Badge>
              </div>
            </Card>

            {/* Details Card */}
            <Card className="p-6 lg:col-span-2">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Employee Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {details.map((detail, index) => {
                  const Icon = detail.icon;
                  return (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <Icon className="w-5 h-5 text-gray-400" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">{detail.label}</p>
                        <p className="text-sm text-gray-900">{detail.value}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>
          </div>

          {/* Additional Information */}
          <div className="mt-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Employment Timeline</h3>
              <div className="relative">
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                <div className="relative space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <Calendar className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Joined Company</p>
                      <p className="text-sm text-gray-500">
                        {new Date(employee.joiningDate).toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Current Role</p>
                      <p className="text-sm text-gray-500">{employee.role} in {employee.department}</p>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeDetail;