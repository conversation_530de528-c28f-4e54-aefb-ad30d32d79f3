import { Employee } from '../types';

export const mockEmployees: Employee[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Engineering',
    role: 'Senior Developer',
    joiningDate: '2022-03-15',
    status: 'Active'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Marketing',
    role: 'Marketing Manager',
    joiningDate: '2021-11-08',
    status: 'Active'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'HR',
    role: 'HR Specialist',
    joiningDate: '2023-01-20',
    status: 'Active'
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Engineering',
    role: 'Junior Developer',
    joiningDate: '2023-07-10',
    status: 'Active'
  },
  {
    id: '5',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Sales',
    role: 'Sales Representative',
    joiningDate: '2022-09-05',
    status: 'Inactive'
  },
  {
    id: '6',
    name: 'Frank Miller',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Finance',
    role: 'Financial Analyst',
    joiningDate: '2021-06-12',
    status: 'Active'
  },
  {
    id: '7',
    name: 'Grace Taylor',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Design',
    role: 'UX Designer',
    joiningDate: '2022-12-03',
    status: 'Active'
  },
  {
    id: '8',
    name: 'Henry Clark',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Operations',
    role: 'Operations Manager',
    joiningDate: '2021-04-18',
    status: 'Active'
  }
];